import { NextRequest, NextResponse } from 'next/server';
import { optionalAuth, requireAuth, withDb } from './lib/auth';

export async function middleware(request: NextRequest) {
  if (request.nextUrl.pathname.startsWith('/api/')) {
    let response: NextResponse | undefined;
    await requireAuth(request as any, {
      status: () => ({ json: (g: any) => (response = NextResponse.json(g)) }) as any,
      json: (g: any) => (response = NextResponse.json(g))
    } as any, () => {});
    if (response) return response;

    await withDb(request as any, {
      status: () => ({ json: (g: any) => (response = NextResponse.json(g)) }) as any,
      json: (g: any) => (response = NextResponse.json(g))
    } as any, () => {});
    if (response) return response;
  }

  return NextResponse.next();
}

export const config = {
  matcher: [
    '/api/:path*',
    '/dashboard/:path*',
  ],
};
