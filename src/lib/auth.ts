
import { Request, Response, NextFunction } from 'express';
import jwt from 'jsonwebtoken';
import { PrismaClient } from '@prisma/client';
import { getDbRouter } from './services/database-router';
import { ensureUserInOrgDatabase } from './services/user-sync.service';

interface JWTPayload {
  id: string;
  email: string;
  displayName: string;
  organizationId?: string;
  role: string;
  tenantId?: string;
  iat?: number;
  exp?: number;
  iss?: string;
}

declare global {
  namespace Express {
    interface Request {
      organizationId?: string;
      organizationSlug?: string;
      db?: PrismaClient;
      user?: {
        id: string;
        email: string;
        displayName: string;
        organizationId: string;
        role: string;
      };
    }
  }
}

export const optionalAuth = async (req: Request, res: Response, next: NextFunction) => {
    try {
        let token: string | null = null;
        const authHeader = req.headers.authorization;
        if (authHeader?.startsWith('Bearer ')) {
            token = authHeader.substring(7);
        } else if (req.headers.cookie) {
            const cookies = req.headers.cookie.split(';').reduce((acc, cookie) => {
                const [key, value] = cookie.trim().split('=');
                acc[key] = value;
                return acc;
            }, {} as Record<string, string>);
            token = cookies['auth-token'];
        }

        if (!token) {
            return next();
        }

        if (!process.env.JWT_SECRET) {
            throw new Error('JWT_SECRET is not configured');
        }

        const decoded = jwt.verify(token, process.env.JWT_SECRET, {
            issuer: 'quantumrhino.cloud'
        }) as JWTPayload;

        req.user = {
            id: decoded.id,
            email: decoded.email,
            displayName: decoded.displayName,
            organizationId: decoded.organizationId || '',
            role: decoded.role || 'VIEWER',
        };
    } catch (error) {
        console.debug('Optional auth failed:', error);
    }
    next();
};

export const requireAuth = async (req: Request, res: Response, next: NextFunction) => {
    await optionalAuth(req, res, () => {
        if (!req.user) {
            return res.status(401).json({ error: 'Authentication required' });
        }
        next();
    });
};

export const requireOrg = async (req: Request, res: Response, next: NextFunction) => {
    if (!req.user?.organizationId) {
        return res.status(400).json({
            error: 'Organization required',
            message: 'Your account is not associated with an organization'
        });
    }
    next();
};

export const withDb = async (req: Request, res: Response, next: NextFunction) => {
    try {
        if (!req.user) {
            return res.status(401).json({ error: 'Authentication required' });
        }

        const organizationId = req.user.organizationId || req.headers['x-organization-id'] as string;

        if (!organizationId) {
            return res.status(400).json({
                error: 'Organization context required',
                message: 'No organization ID provided in token or headers'
            });
        }

        const dbRouter = getDbRouter();
        const orgs = await dbRouter.getUserOrganizations(req.user.id);
        const hasAccess = orgs.some(org => org.id === organizationId);

        if (!hasAccess) {
            return res.status(403).json({
                error: 'Access denied',
                message: 'You do not have access to this organization'
            });
        }

        const org = await dbRouter.getOrganization(organizationId);
        if (!org) {
            return res.status(404).json({
                error: 'Organization not found',
                message: 'The specified organization does not exist'
            });
        }

        if (org.status !== 'active') {
            return res.status(403).json({
                error: 'Organization inactive',
                message: 'This organization is currently inactive'
            });
        }

        const db = await dbRouter.getConnection(organizationI d);
        await ensureUserInOrgDatabase(db, req.user);

        req.organizationId = organizationId;
        req.organizationSlug = org.slug;
        req.db = db;

        next();
    } catch (error) {
        console.error('Database connection error:', error);
        return res.status(503).json({ error: 'Could not connect to organization database' });
    }
};
