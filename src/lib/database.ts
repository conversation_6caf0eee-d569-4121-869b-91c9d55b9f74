import { PrismaClient } from '@prisma/client';
import { getDbRouter } from './services/database-router';

export const getOrgDb = async (organizationId: string) => {
  const dbRouter = getDbRouter();
  return await dbRouter.getConnection(organizationId);
};

// Database service functions
export class DatabaseService {
  
  /**
   * Get or create user by email
   */
  static async getOrCreateUser(email: string, name?: string, avatar?: string) {
    let user = await prisma.user.findUnique({
      where: { email },
      include: { organization: true }
    });

    if (!user) {
      user = await prisma.user.create({
        data: {
          email,
          name: name || email.split('@')[0],
          avatar
        },
        include: { organization: true }
      });
    }

    return user;
  }

  /**
   * Get user templates
   */
  static async getUserTemplates(userId: string) {
    return await prisma.template.findMany({
      where: { userId },
      orderBy: { createdAt: 'desc' }
    });
  }

  /**
   * Create template
   */
  static async createTemplate(data: {
    name: string;
    description?: string;
    filePath: string;
    fileName: string;
    fileSize: number;
    mimeType: string;
    userId: string;
    isDefault?: boolean;
  }) {
    return await prisma.template.create({
      data
    });
  }

  /**
   * Get user companies
   */
  static async getUserCompanies(userId: string) {
    return await prisma.company.findMany({
      where: { userId },
      orderBy: { name: 'asc' }
    });
  }

  /**
   * Create company
   */
  static async createCompany(data: {
    name: string;
    address?: string;
    email?: string;
    phone?: string;
    website?: string;
    contactPerson?: string;
    userId: string;
  }) {
    return await prisma.company.create({
      data
    });
  }

  /**
   * Get user SOWs
   */
  static async getUserSOWs(userId: string, limit?: number) {
    return await prisma.sOW.findMany({
      where: { userId },
      include: {
        template: true,
        company: true
      },
      orderBy: { createdAt: 'desc' },
      take: limit
    });
  }

  /**
   * Create SOW
   */
  static async createSOW(data: {
    projectName: string;
    clientName: string;
    vendorName: string;
    scopeOfWork: string;
    timeline: string;
    pricing: string;
    templateId?: string;
    companyId?: string;
    userId: string;
    filePath?: string;
    fileName?: string;
    fileSize?: number;
  }) {
    return await prisma.sOW.create({
      data,
      include: {
        template: true,
        company: true
      }
    });
  }

  /**
   * Update SOW
   */
  static async updateSOW(id: string, data: Partial<{
    projectName: string;
    clientName: string;
    vendorName: string;
    scopeOfWork: string;
    timeline: string;
    pricing: string;
    templateId: string;
    companyId: string;
    filePath: string;
    fileName: string;
    fileSize: number;
    status: 'DRAFT' | 'REVIEW' | 'APPROVED' | 'SENT' | 'SIGNED' | 'COMPLETED';
  }>) {
    return await prisma.sOW.update({
      where: { id },
      data,
      include: {
        template: true,
        company: true
      }
    });
  }

  /**
   * Delete SOW
   */
  static async deleteSOW(id: string, userId: string) {
    return await prisma.sOW.delete({
      where: { 
        id,
        userId // Ensure user owns the SOW
      }
    });
  }

  /**
   * Get SOW by ID
   */
  static async getSOW(id: string, userId: string) {
    return await prisma.sOW.findFirst({
      where: { 
        id,
        userId // Ensure user owns the SOW
      },
      include: {
        template: true,
        company: true
      }
    });
  }

  /**
   * Get template by ID
   */
  static async getTemplate(id: string, userId: string) {
    return await prisma.template.findFirst({
      where: { 
        id,
        userId // Ensure user owns the template
      }
    });
  }

  /**
   * Delete template
   */
  static async deleteTemplate(id: string, userId: string) {
    return await prisma.template.delete({
      where: { 
        id,
        userId // Ensure user owns the template
      }
    });
  }

  /**
   * Get user dashboard stats
   */
  static async getUserStats(userId: string) {
    const [templatesCount, sowsCount, companiesCount, recentSOWs] = await Promise.all([
      prisma.template.count({ where: { userId } }),
      prisma.sOW.count({ where: { userId } }),
      prisma.company.count({ where: { userId } }),
      prisma.sOW.findMany({
        where: { userId },
        include: { template: true, company: true },
        orderBy: { createdAt: 'desc' },
        take: 5
      })
    ]);

    return {
      templates: templatesCount,
      sows: sowsCount,
      companies: companiesCount,
      recentSOWs
    };
  }

  /**
   * Search SOWs
   */
  static async searchSOWs(userId: string, query: string) {
    return await prisma.sOW.findMany({
      where: {
        userId,
        OR: [
          { projectName: { contains: query, mode: 'insensitive' } },
          { clientName: { contains: query, mode: 'insensitive' } },
          { vendorName: { contains: query, mode: 'insensitive' } }
        ]
      },
      include: {
        template: true,
        company: true
      },
      orderBy: { createdAt: 'desc' }
    });
  }

  /**
   * Get company by ID
   */
  static async getCompany(id: string, userId: string) {
    return await prisma.company.findFirst({
      where: { 
        id,
        userId // Ensure user owns the company
      }
    });
  }

  /**
   * Update company
   */
  static async updateCompany(id: string, userId: string, data: Partial<{
    name: string;
    address: string;
    email: string;
    phone: string;
    website: string;
    contactPerson: string;
  }>) {
    return await prisma.company.update({
      where: { 
        id,
        userId // Ensure user owns the company
      },
      data
    });
  }

  /**
   * Delete company
   */
  static async deleteCompany(id: string, userId: string) {
    return await prisma.company.delete({
      where: { 
        id,
        userId // Ensure user owns the company
      }
    });
  }
}
