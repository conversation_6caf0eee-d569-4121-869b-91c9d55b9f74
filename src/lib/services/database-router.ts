/**
 * Database Router for Multi-Tenant Architecture
 * 
 * This service manages database connections for different organizations
 * in the QuantumRhino SaaS platform. Each organization has its own
 * isolated database for complete data separation.
 */

import { PrismaClient } from '@prisma/client';
import crypto from 'crypto';

interface DatabaseConfig {
  organizationId: string;
  connectionString: string;
  maxConnections: number;
  isActive: boolean;
}

interface OrganizationInfo {
  id: string;
  name: string;
  slug: string;
  databaseName: string;
  tier: string;
  status: string;
}

export class DatabaseRouter {
  private masterDb: PrismaClient;
  private connections: Map<string, PrismaClient> = new Map();
  private configCache: Map<string, DatabaseConfig> = new Map();
  private algorithm = 'aes-256-gcm';

  constructor() {
    if (!process.env.MASTER_DATABASE_URL) {
      throw new Error('MASTER_DATABASE_URL is required');
    }

    this.masterDb = new PrismaClient({
      datasources: {
        db: { url: process.env.MASTER_DATABASE_URL }
      }
    });
  }

  /**
   * Get database connection for a specific organization
   */
  async getConnection(organizationId: string): Promise<PrismaClient> {
    // Check connection cache
    if (this.connections.has(organizationId)) {
      const connection = this.connections.get(organizationId)!;
      // Verify connection is still alive
      try {
        await connection.$queryRaw`SELECT 1`;
        return connection;
      } catch {
        // Connection is dead, remove from cache
        this.connections.delete(organizationId);
      }
    }

    // Get configuration
    const config = await this.getConfig(organizationId);
    
    if (!config || !config.isActive) {
      throw new Error(`No active database configuration for organization ${organizationId}`);
    }

    // Create new connection
    const connection = new PrismaClient({
      datasources: {
        db: { url: config.connectionString }
      },
      log: process.env.NODE_ENV === 'development' ? ['query', 'error', 'warn'] : ['error']
    });

    // Test connection
    try {
      await connection.$connect();
    } catch (error) {
      throw new Error(`Failed to connect to organization database: ${error}`);
    }

    // Cache connection
    this.connections.set(organizationId, connection);

    // Set up connection cleanup on idle
    this.setupIdleTimeout(organizationId);

    return connection;
  }

  /**
   * Get organization information
   */
  async getOrganization(organizationId: string): Promise<OrganizationInfo | null> {
    try {
      const org = await this.masterDb.$queryRaw<OrganizationInfo[]>`
        SELECT 
          id, 
          name, 
          slug, 
          database_name as "databaseName",
          tier,
          status
        FROM master.organizations 
        WHERE id = ${organizationId}::uuid
        LIMIT 1
      `;

      return org[0] || null;
    } catch (error) {
      console.error('Failed to get organization:', error);
      return null;
    }
  }

  /**
   * Get organizations for a user
   */
  async getUserOrganizations(userId: string): Promise<OrganizationInfo[]> {
    try {
      const orgs = await this.masterDb.$queryRaw<OrganizationInfo[]>`
        SELECT 
          o.id,
          o.name,
          o.slug,
          o.database_name as "databaseName",
          o.tier,
          o.status,
          om.role
        FROM master.organizations o
        JOIN master.organization_memberships om ON o.id = om.organization_id
        WHERE om.global_user_id = ${userId}::uuid
          AND om.is_active = true
          AND o.status = 'active'
        ORDER BY o.created_at DESC
      `;

      return orgs;
    } catch (error) {
      console.error('Failed to get user organizations:', error);
      return [];
    }
  }

  /**
   * Close connection for an organization
   */
  async closeConnection(organizationId: string): Promise<void> {
    const connection = this.connections.get(organizationId);
    if (connection) {
      await connection.$disconnect();
      this.connections.delete(organizationId);
    }
  }

  /**
   * Close all connections (for graceful shutdown)
   */
  async closeAllConnections(): Promise<void> {
    const promises = Array.from(this.connections.entries()).map(([orgId, connection]) => {
      return connection.$disconnect().then(() => {
        this.connections.delete(orgId);
      });
    });

    await Promise.all(promises);
    await this.masterDb.$disconnect();
  }

  /**
   * Get database configuration from master database
   */
  private async getConfig(organizationId: string): Promise<DatabaseConfig | null> {
    // Check cache first
    if (this.configCache.has(organizationId)) {
      return this.configCache.get(organizationId)!;
    }

    try {
      const config = await this.masterDb.$queryRaw<any[]>`
        SELECT 
          dc.organization_id as "organizationId",
          dc.connection_string_encrypted as "connectionStringEncrypted",
          dc.max_connections as "maxConnections",
          dc.is_active as "isActive"
        FROM master.database_configs dc
        WHERE dc.organization_id = ${organizationId}::uuid
          AND dc.is_active = true
        ORDER BY dc.created_at DESC
        LIMIT 1
      `;

      if (!config[0]) {
        return null;
      }

      // Decrypt connection string
      const connectionString = this.decrypt(config[0].connectionStringEncrypted);

      const dbConfig: DatabaseConfig = {
        organizationId: config[0].organizationId,
        connectionString,
        maxConnections: config[0].maxConnections || 20,
        isActive: config[0].isActive
      };

      // Cache for 5 minutes
      this.configCache.set(organizationId, dbConfig);
      setTimeout(() => {
        this.configCache.delete(organizationId);
      }, 5 * 60 * 1000);

      return dbConfig;
    } catch (error) {
      console.error('Failed to get database config:', error);
      return null;
    }
  }

  /**
   * Set up idle timeout for connection cleanup
   */
  private setupIdleTimeout(organizationId: string): void {
    // Close connection after 10 minutes of inactivity
    setTimeout(() => {
      if (this.connections.has(organizationId)) {
        this.closeConnection(organizationId).catch(console.error);
      }
    }, 10 * 60 * 1000);
  }

  /**
   * Encrypt a string using AES-256-GCM
   */
  encrypt(text: string): string {
    const key = Buffer.from(process.env.ENCRYPTION_KEY || '', 'hex');
    const iv = crypto.randomBytes(16);
    const cipher = crypto.createCipheriv(this.algorithm, key, iv);
    
    let encrypted = cipher.update(text, 'utf8', 'hex');
    encrypted += cipher.final('hex');
    
    const authTag = cipher.getAuthTag();
    
    return iv.toString('hex') + ':' + authTag.toString('hex') + ':' + encrypted;
  }

  /**
   * Decrypt a string using AES-256-GCM
   */
  private decrypt(encryptedData: string): string {
    const parts = encryptedData.split(':');
    const iv = Buffer.from(parts.shift()!, 'hex');
    const authTag = Buffer.from(parts.shift()!, 'hex');
    const encrypted = parts.join(':');
    
    const key = Buffer.from(process.env.ENCRYPTION_KEY || '', 'hex');
    const decipher = crypto.createDecipheriv(this.algorithm, key, iv);
    decipher.setAuthTag(authTag);
    
    let decrypted = decipher.update(encrypted, 'hex', 'utf8');
    decrypted += decipher.final('utf8');
    
    return decrypted;
  }
}

// Singleton instance
let dbRouter: DatabaseRouter | null = null;

export function getDbRouter(): DatabaseRouter {
  if (!dbRouter) {
    dbRouter = new DatabaseRouter();
  }
  return dbRouter;
}

// Graceful shutdown (only in Node.js runtime, not Edge Runtime)
if (typeof process !== 'undefined' && process.on) {
  process.on('SIGINT', async () => {
    console.log('Closing database connections...');
    if (dbRouter) {
      await dbRouter.closeAllConnections();
    }
    process.exit(0);
  });
}